{"name": "app-platform-demo-backend", "version": "1.0.0", "license": "MIT", "description": "A demo app to showcase usage of the Pi App Platform.", "author": {"name": "Pi Core Team"}, "scripts": {"build": "tsc", "start": "NODE_ENV=development ts-node-dev ./src/index.ts"}, "dependencies": {"@types/connect-mongo": "^3.1.3", "@types/cookie-parser": "^1.4.2", "@types/express-session": "^1.17.5", "axios": "^0.21.1", "connect-mongo": "^4.4.1", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.17.1", "express-session": "^1.17.2", "mongodb": "^4.0.0", "morgan": "^1.10.0"}, "devDependencies": {"@types/cors": "^2.8.11", "@types/express": "^4.17.12", "@types/morgan": "^1.9.2", "@types/node": "^18.7.23", "ts-node-dev": "^2.0.0", "typescript": "^4.7.4"}}