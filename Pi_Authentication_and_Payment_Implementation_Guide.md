# Pi Network Authentication and Payment Implementation Guide

This document explains how authentication and payment systems are implemented in the Pi Demo App, providing a comprehensive guide for implementing similar functionality in other applications.

## Overview

The Pi Demo App demonstrates a complete implementation of:
1. **User Authentication** using Pi SDK and Pi Platform API
2. **Payment Processing** with Pi cryptocurrency
3. **Session Management** with Express sessions
4. **Database Integration** with MongoDB

## Architecture

### Tech Stack
- **Frontend**: React with TypeScript
- **Backend**: Node.js with Express
- **Database**: MongoDB
- **Authentication**: Pi SDK + Pi Platform API
- **Session Store**: MongoDB with express-session
- **Payment**: Pi Network blockchain

### Project Structure
```
├── frontend/          # React frontend app
├── backend/           # Express.js backend API
├── reverse-proxy/     # Nginx reverse proxy
└── docker-compose.yml # Container orchestration
```

## Authentication System

### 1. Frontend Authentication Flow

#### Pi SDK Integration
```typescript
// Include Pi SDK in HTML
<script src="https://sdk.minepi.com/pi-sdk.js"></script>

// Initialize Pi SDK
Pi.init({ version: "2.0", sandbox: runSDKInSandboxMode });
```

#### User Sign-In Process
```typescript
const signIn = async () => {
  const scopes = ['username', 'payments'];
  const authResult: AuthResult = await window.Pi.authenticate(scopes, onIncompletePaymentFound);
  
  // Send auth result to backend for verification
  signInUser(authResult);
  
  // Update local state
  setUser(authResult.user);
}

const signInUser = (authResult: AuthResult) => {
  axiosClient.post('/user/signin', {authResult});
}
```

#### AuthResult Structure
```typescript
type AuthResult = {
  accessToken: string,
  user: {
    uid: string,
    username: string,
    roles: Array<string>
  }
}
```

### 2. Backend Authentication Flow

#### Access Token Verification
```typescript
app.post('/signin', async (req, res) => {
  const auth = req.body.authResult;
  
  try {
    // Verify access token with Pi Platform API
    const me = await platformAPIClient.get(`/v2/me`, { 
      headers: { 'Authorization': `Bearer ${auth.accessToken}` } 
    });
    
    // Token is valid, proceed with user creation/update
  } catch (err) {
    return res.status(401).json({error: "Invalid access token"});
  }
});
```

#### User Data Management
```typescript
// Check if user exists
let currentUser = await userCollection.findOne({ uid: auth.user.uid });

if (currentUser) {
  // Update existing user's access token
  await userCollection.updateOne({
    _id: currentUser._id
  }, {
    $set: { accessToken: auth.accessToken }
  });
} else {
  // Create new user
  const insertResult = await userCollection.insertOne({
    username: auth.user.username,
    uid: auth.user.uid,
    roles: auth.user.roles,
    accessToken: auth.accessToken
  });
}

// Store user in session
req.session.currentUser = currentUser;
```

### 3. Session Management

#### Express Session Configuration
```typescript
app.use(session({
  secret: env.session_secret,
  resave: false,
  saveUninitialized: false,
  store: MongoStore.create({
    mongoUrl: mongoUri,
    mongoOptions: mongoClientOptions,
    dbName: dbName,
    collectionName: 'user_sessions'
  }),
}));
```

#### Session Types
```typescript
declare module 'express-session' {
  export interface SessionData {
    currentUser: UserData | null,
  }
}
```

### 4. Platform API Client Setup

```typescript
const platformAPIClient = axios.create({
  baseURL: env.platform_api_url, // https://api.minepi.com
  timeout: 20000,
  headers: { 'Authorization': `Key ${env.pi_api_key}` }
});
```

## Payment System

### 1. Frontend Payment Flow

#### Payment Initiation
```typescript
const orderProduct = async (memo: string, amount: number, paymentMetadata: MyPaymentMetadata) => {
  if(user === null) {
    return setShowModal(true); // Require authentication
  }
  
  const paymentData = { amount, memo, metadata: paymentMetadata };
  const callbacks = {
    onReadyForServerApproval,
    onReadyForServerCompletion,
    onCancel,
    onError
  };
  
  const payment = await window.Pi.createPayment(paymentData, callbacks);
};
```

#### Payment Callbacks Implementation
```typescript
// 1. Server Approval Callback
const onReadyForServerApproval = (paymentId: string) => {
  axiosClient.post('/payments/approve', {paymentId});
}

// 2. Server Completion Callback  
const onReadyForServerCompletion = (paymentId: string, txid: string) => {
  axiosClient.post('/payments/complete', {paymentId, txid});
}

// 3. Payment Cancellation
const onCancel = (paymentId: string) => {
  axiosClient.post('/payments/cancelled_payment', {paymentId});
}

// 4. Error Handling
const onError = (error: Error, payment?: PaymentDTO) => {
  console.log("Payment error:", error);
  if (payment) {
    // Handle specific payment errors
  }
}
```

#### Incomplete Payment Handling
```typescript
const onIncompletePaymentFound = (payment: PaymentDTO) => {
  // Called during authentication if user has incomplete payments
  return axiosClient.post('/payments/incomplete', {payment});
}
```

### 2. Backend Payment Flow

#### Payment Approval Endpoint
```typescript
app.post('/approve', async (req, res) => {
  if (!req.session.currentUser) {
    return res.status(401).json({ error: 'unauthorized' });
  }

  const paymentId = req.body.paymentId;
  const currentPayment = await platformAPIClient.get(`/v2/payments/${paymentId}`);
  
  // Create order record in database
  await orderCollection.insertOne({
    pi_payment_id: paymentId,
    product_id: currentPayment.data.metadata.productId,
    user: req.session.currentUser.uid,
    txid: null,
    paid: false,
    cancelled: false,
    created_at: new Date()
  });

  // Approve payment with Pi Platform
  await platformAPIClient.post(`/v2/payments/${paymentId}/approve`);
  return res.status(200).json({ message: `Approved payment ${paymentId}` });
});
```

#### Payment Completion Endpoint
```typescript
app.post('/complete', async (req, res) => {
  const { paymentId, txid } = req.body;
  
  // Update order as paid
  await orderCollection.updateOne(
    { pi_payment_id: paymentId }, 
    { $set: { txid: txid, paid: true } }
  );

  // Notify Pi Platform of completion
  await platformAPIClient.post(`/v2/payments/${paymentId}/complete`, { txid });
  return res.status(200).json({ message: `Completed payment ${paymentId}` });
});
```

#### Incomplete Payment Handling
```typescript
app.post('/incomplete', async (req, res) => {
  const payment = req.body.payment;
  const paymentId = payment.identifier;
  const txid = payment.transaction?.txid;
  const txURL = payment.transaction?._link;

  // Find the incomplete order
  const order = await orderCollection.findOne({ pi_payment_id: paymentId });
  if (!order) {
    return res.status(400).json({ message: "Order not found" });
  }

  // Verify transaction on Pi blockchain
  const horizonResponse = await axios.get(txURL);
  const paymentIdOnBlock = horizonResponse.data.memo;

  if (paymentIdOnBlock !== order.pi_payment_id) {
    return res.status(400).json({ message: "Payment id doesn't match." });
  }

  // Mark order as paid and complete payment
  await orderCollection.updateOne(
    { pi_payment_id: paymentId }, 
    { $set: { txid, paid: true } }
  );
  
  await platformAPIClient.post(`/v2/payments/${paymentId}/complete`, { txid });
});
```

## Database Schema

### User Collection
```typescript
interface UserData {
  _id: ObjectId,
  username: string,
  uid: string,           // Pi Network user ID
  roles: Array<string>,
  accessToken: string    // Current Pi access token
}
```

### Orders Collection
```javascript
{
  pi_payment_id: string,    // Pi payment identifier
  product_id: string,       // Product being purchased
  user: string,             // User UID
  txid: string | null,      // Blockchain transaction ID
  paid: boolean,            // Payment status
  cancelled: boolean,       // Cancellation status
  created_at: Date          // Order creation timestamp
}
```

### Sessions Collection
Managed automatically by `connect-mongo` for Express sessions.

## Environment Configuration

### Backend Environment Variables
```bash
# Pi Platform Integration
PI_API_KEY=your_pi_api_key
PLATFORM_API_URL=https://api.minepi.com

# Session Security
SESSION_SECRET=your_random_session_secret

# Database
MONGO_HOST=localhost:27017
MONGODB_DATABASE_NAME=demoapp
MONGODB_USERNAME=demoapp
MONGODB_PASSWORD=your_password

# CORS
FRONTEND_URL=http://localhost:3314
```

### Frontend Environment Variables
```bash
REACT_APP_BACKEND_URL=http://localhost:8000
REACT_APP_SANDBOX_SDK=true  # Use sandbox for development
```

## Security Considerations

### 1. Access Token Verification
- **Never trust frontend data**: Always verify access tokens on the backend
- **Use Pi Platform API**: The `/v2/me` endpoint is the source of truth
- **Handle token expiration**: Implement proper error handling for invalid tokens

### 2. Payment Security
- **Server-side validation**: All payment logic must be validated on the backend
- **Blockchain verification**: Verify transactions using the Pi blockchain
- **Idempotency**: Handle duplicate payment requests gracefully

### 3. Session Security
- **Secure session storage**: Use MongoDB for session persistence
- **CORS configuration**: Properly configure CORS for your domain
- **Session secrets**: Use strong, random session secrets

## Implementation Checklist

### Prerequisites
- [ ] Register app on Pi Developer Portal
- [ ] Obtain Pi API key
- [ ] Set up MongoDB database
- [ ] Configure environment variables

### Frontend Setup
- [ ] Include Pi SDK script
- [ ] Initialize Pi SDK with sandbox/production mode
- [ ] Implement authentication flow
- [ ] Implement payment callbacks
- [ ] Configure axios client with credentials

### Backend Setup
- [ ] Set up Express with session middleware
- [ ] Configure MongoDB connection
- [ ] Implement user authentication endpoints
- [ ] Implement payment processing endpoints
- [ ] Set up Pi Platform API client
- [ ] Configure CORS properly

### Testing
- [ ] Test authentication flow in Pi Sandbox
- [ ] Test payment approval process
- [ ] Test payment completion process
- [ ] Test incomplete payment handling
- [ ] Test error scenarios

This implementation provides a robust foundation for Pi Network integration with proper authentication, payment processing, and security measures.
