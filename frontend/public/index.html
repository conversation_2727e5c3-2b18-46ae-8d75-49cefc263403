<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->

    <script>
      // Hacking react-scripts method for defining environment variables because we want to be able to
      // pass them at runtime when the container starts, instead of having to build the frontend once for
      // each environment (e.g staging / production).
      //
      // This will be automatically templated:
      // - in DEV, by react-scripts (see https://create-react-app.dev/docs/adding-custom-environment-variables/)
      // - in PROD, by the container's entrypoint script found in docker/entrypoint.sh (this enables passing the
      //   variables at runtime instead of consuming it at build time and building a non-portable container)
      window.__ENV = {
        backendURL: "%REACT_APP_BACKEND_URL%",
        sandbox: "%REACT_APP_SANDBOX_SDK%",
      }
    </script>

    <!-- Import the Pi Network App Platform SDK: -->
    <script src="https://sdk.minepi.com/pi-sdk.js"></script>
    
    <!-- TODO make sandbox conditional to env (dev / prod) -->
    <script>
      // Make the decision whether Pi SDK should run in "Sandbox" mode or "Pi Browser" mode based on the
      // dedicated environment variable:
      var runSDKInSandboxMode = window.__ENV.sandbox === "true"; // treat env vars as strings, not booleans
      
      // Init Pi SDK:
      Pi.init({ version: "2.0", sandbox: runSDKInSandboxMode });
    </script>
    
    <title>React App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
