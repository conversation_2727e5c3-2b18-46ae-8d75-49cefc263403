# Pi Demo App Frontend

Pi Demo App is an example React app created with [Create React App](https://create-react-app.dev/).

### 1. Install dependencies:

You will need a working NodeJS installation, and `yarn`. **The demo app frontend isn't meant to support npm**.
In most cases, `yarn` will come along with your NodeJS installation.

Install dependencies by running `yarn install`.

### 2. Start the app:

Run the following command to start the app's development server: `yarn start`
This will open a browser window on http://localhost:3314 which you can close, since the demo app mostly needs
to run inside of the Pi Sandbox environment in order to work correctly in development.

---
You've completed the frontend setup, return to [`doc/development.md`](../doc/development.md) to finish setting up the demo app
