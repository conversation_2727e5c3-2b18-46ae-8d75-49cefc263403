server {
  listen        80;
  root          /var/www/webapp;

  #charset koi8-r;
  #access_log  /var/log/nginx/log/host.access.log  main;

  gzip             on;
  gzip_comp_level  2;
  gzip_min_length  1000;
  gzip_proxied     expired no-cache no-store private auth;
  gzip_types       text/plain application/x-javascript text/xml text/css application/xml;

  location / {
    # First attempt to serve the requested file, then directory, then fall back to index.html, 
    # which loads the react app, as compiled by webpack using `react-scripts build`.
    try_files $uri $uri/ /index.html;

    # Don't cache the HTML files so that the hashed JS filenames work:
    location ~ ^.+\.(html?)$ {
        add_header Cache-Control no-cache;
    }
  }


  # redirect server error pages to the static page /50x.html
  #
  # error_page  500 502 503 504  /50x.html;
  # location = /50x.html {
  #     root   /usr/share/nginx/html;
  # }
}
